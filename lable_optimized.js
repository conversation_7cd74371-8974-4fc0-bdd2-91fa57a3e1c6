// ==UserScript==
// @name         腾讯质检平台 - 全能自动处理 (优化版)
// @namespace    http://tampermonkey.net/
// @version      19.0
// @description  【优化版 v19.0】重构代码结构，提升性能和稳定性，增强错误处理和用户体验
// <AUTHOR> Assistant (Optimized Version)
// @match        https://qlabel.tencent.com/workbench/tasks*
// @match        https://qlabel.tencent.com/workbench/*
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// ==/UserScript==

(function() {
    'use strict';
    
    // ===== 配置管理 =====
    class ConfigManager {
        static defaults = {
            minDelay: 1000,
            maxDelay: 3000,
            defaultChoices: ['1', '0'],
            currentMode: 'modify',
            maxRetries: 15,
            checkInterval: 1000
        };
        
        static get(key) {
            return GM_getValue(key, this.defaults[key]);
        }
        
        static set(key, value) {
            GM_setValue(key, value);
        }
        
        static getRandomDelay() {
            const min = this.get('minDelay');
            const max = this.get('maxDelay');
            return min + Math.random() * (max - min);
        }
    }
    
    // ===== 日志管理 =====
    class Logger {
        static log(message, level = 'INFO') {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${level} v19.0 ${timestamp}] ${message}`);
        }
        
        static error(message, error = null) {
            this.log(message, 'ERROR');
            if (error) console.error(error);
        }
        
        static debug(message) {
            this.log(message, 'DEBUG');
        }
    }
    
    // ===== DOM 工具类 =====
    class DOMUtils {
        static findButtonByText(text) {
            return Array.from(document.querySelectorAll('button, a'))
                        .find(el => el.textContent.trim().includes(text));
        }
        
        static dispatchClick(element) {
            if (!element) return false;
            try {
                const event = new MouseEvent('click', { 
                    bubbles: true, 
                    cancelable: true,
                    view: window
                });
                element.dispatchEvent(event);
                return true;
            } catch (error) {
                Logger.error('点击元素失败', error);
                return false;
            }
        }
        
        static waitForElement(selector, timeout = 10000) {
            return new Promise((resolve, reject) => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }
                
                const observer = new MutationObserver(() => {
                    const element = document.querySelector(selector);
                    if (element) {
                        observer.disconnect();
                        resolve(element);
                    }
                });
                
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
                
                setTimeout(() => {
                    observer.disconnect();
                    reject(new Error(`元素 ${selector} 在 ${timeout}ms 内未找到`));
                }, timeout);
            });
        }
    }
    
    // ===== 状态管理 =====
    class StateManager {
        constructor() {
            this.isRunning = false;
            this.currentMode = ConfigManager.get('currentMode');
            this.timers = new Set();
            this.ui = null;
        }
        
        setRunning(running) {
            this.isRunning = running;
            this.updateUI();
        }
        
        setMode(mode) {
            this.currentMode = mode;
            ConfigManager.set('currentMode', mode);
            this.updateUI();
        }
        
        clearAllTimers() {
            this.timers.forEach(timer => clearTimeout(timer));
            this.timers.clear();
        }
        
        addTimer(timer) {
            this.timers.add(timer);
            return timer;
        }
        
        updateUI() {
            if (this.ui) {
                this.ui.updateState(this.isRunning, this.currentMode);
            }
        }
    }
    
    // ===== 处理引擎 =====
    class ProcessingEngine {
        constructor(stateManager) {
            this.state = stateManager;
            this.retryCount = 0;
        }
        
        async processTask() {
            try {
                this.state.ui?.updateStatus('开始处理任务...', 'info');
                
                if (this.state.currentMode === 'modify') {
                    await this.processModification();
                } else {
                    await this.processAutoPass();
                }
                
                this.state.ui?.updateStatus('任务处理完成', 'success');
                this.retryCount = 0;
            } catch (error) {
                Logger.error('处理任务失败', error);
                this.state.ui?.updateStatus(`处理失败: ${error.message}`, 'error');
                this.handleError(error);
            }
        }
        
        async processModification() {
            // 步骤1: 点击修改标注
            await this.clickModifyButton();
            await this.delay(700);
            
            // 步骤2: 选择选项
            await this.selectOptions();
            await this.delay(500);
            
            // 步骤3: 提交修改
            await this.submitModification();
            
            // 步骤4: 确认提交
            await this.confirmSubmission();
        }
        
        async clickModifyButton() {
            const button = DOMUtils.findButtonByText('修改标注');
            if (!button) {
                throw new Error('未找到"修改标注"按钮');
            }
            
            if (!DOMUtils.dispatchClick(button)) {
                throw new Error('无法点击"修改标注"按钮');
            }
            
            this.state.ui?.updateStatus('已点击修改标注按钮', 'info');
        }
        
        async selectOptions() {
            const questionBlocks = document.querySelectorAll('.ivu-card-body .ivu-form-item');
            if (questionBlocks.length < 2) {
                throw new Error('未找到足够的问题区块');
            }
            
            const questionIndices = [1, 8];
            const choices = ConfigManager.get('defaultChoices');
            
            for (let i = 0; i < 2; i++) {
                const questionNum = questionIndices[i];
                const choice = choices[i];
                
                if (!this.isQuestionEnabled(questionNum)) {
                    continue;
                }
                
                await this.selectQuestionOption(questionBlocks[i], questionNum, choice);
            }
        }
        
        isQuestionEnabled(questionNum) {
            const checkbox = document.getElementById(`q${questionNum}_enable`);
            return checkbox && checkbox.checked;
        }
        
        async selectQuestionOption(block, questionNum, choice) {
            const wrapperOptions = block.querySelectorAll('.ivu-checkbox-wrapper, .ivu-radio-wrapper');
            
            for (const wrapper of wrapperOptions) {
                const textNode = Array.from(wrapper.childNodes)
                    .find(node => node.nodeType === Node.TEXT_NODE && 
                                 node.textContent.trim() === choice);
                
                if (textNode) {
                    const isChecked = wrapper.classList.contains('ivu-checkbox-wrapper-checked') || 
                                    wrapper.classList.contains('ivu-radio-wrapper-checked');
                    
                    if (!isChecked) {
                        const input = wrapper.querySelector('input[type="checkbox"], input[type="radio"]');
                        if (input && !DOMUtils.dispatchClick(input)) {
                            throw new Error(`无法选择问题${questionNum}的选项${choice}`);
                        }
                    }
                    return;
                }
            }
            
            throw new Error(`问题${questionNum}的选项"${choice}"未找到`);
        }
        
        async submitModification() {
            const button = DOMUtils.findButtonByText('提交修改');
            if (!button) {
                throw new Error('未找到"提交修改"按钮');
            }
            
            if (!DOMUtils.dispatchClick(button)) {
                throw new Error('无法点击"提交修改"按钮');
            }
            
            this.state.ui?.updateStatus('已提交修改', 'info');
        }
        
        async confirmSubmission() {
            try {
                const confirmButton = await DOMUtils.waitForElement('.ivu-modal-footer .ivu-btn-primary', 3000);
                
                if (!DOMUtils.dispatchClick(confirmButton)) {
                    throw new Error('无法点击确认按钮');
                }
                
                this.state.ui?.updateStatus('已确认提交', 'success');
            } catch (error) {
                throw new Error('确认提交超时或失败');
            }
        }
        
        async processAutoPass() {
            const button = DOMUtils.findButtonByText('质检通过') ||
                          DOMUtils.findButtonByText('通过');
            
            if (!button) {
                throw new Error('未找到"质检通过"按钮');
            }
            
            if (!DOMUtils.dispatchClick(button)) {
                throw new Error('无法点击"质检通过"按钮');
            }
            
            this.state.ui?.updateStatus('已点击质检通过', 'success');
        }
        
        handleError(error) {
            this.retryCount++;
            const maxRetries = ConfigManager.get('maxRetries');

            Logger.error(`处理失败: ${error.message}`, error);

            if (this.retryCount < maxRetries) {
                Logger.log(`第${this.retryCount}次重试...`);
                const timer = setTimeout(() => this.processTask(), 2000);
                this.state.addTimer(timer);
            } else {
                Logger.error('达到最大重试次数，停止处理');
                this.state.setRunning(false);
                this.retryCount = 0;
            }
        }
        
        delay(ms) {
            return new Promise(resolve => {
                const timer = setTimeout(resolve, ms);
                this.state.addTimer(timer);
            });
        }
    }

    // ===== 自动化控制器 =====
    class AutomationController {
        constructor(stateManager, processingEngine) {
            this.state = stateManager;
            this.engine = processingEngine;
            this.checkTimer = null;
        }

        start() {
            if (this.state.isRunning) return;

            this.state.setRunning(true);
            this.state.ui?.updateStatus('自动化已启动', 'success');
            this.startMonitoring();
        }

        stop() {
            this.state.setRunning(false);
            this.stopMonitoring();
            this.state.clearAllTimers();
            this.state.ui?.updateStatus('自动化已停止', 'warning');
        }

        startMonitoring() {
            const checkInterval = ConfigManager.get('checkInterval');

            this.checkTimer = setInterval(() => {
                if (!this.state.isRunning) {
                    this.stopMonitoring();
                    return;
                }

                this.checkForTasks();
            }, checkInterval);
        }

        stopMonitoring() {
            if (this.checkTimer) {
                clearInterval(this.checkTimer);
                this.checkTimer = null;
            }
        }

        checkForTasks() {
            let targetButton = null;

            if (this.state.currentMode === 'modify') {
                targetButton = DOMUtils.findButtonByText('修改标注');
            } else {
                targetButton = DOMUtils.findButtonByText('质检通过') ||
                              DOMUtils.findButtonByText('通过');
            }

            if (targetButton) {
                this.stopMonitoring();
                this.scheduleProcessing();
            }
        }

        scheduleProcessing() {
            const delay = ConfigManager.getRandomDelay();
            const seconds = Math.round(delay / 1000);

            this.state.ui?.updateStatus(`检测到任务，${seconds}秒后处理...`, 'info');

            const timer = setTimeout(() => {
                if (this.state.isRunning) {
                    this.engine.processTask().finally(() => {
                        if (this.state.isRunning) {
                            this.startMonitoring();
                        }
                    });
                }
            }, delay);

            this.state.addTimer(timer);
        }

        processSingle() {
            this.engine.processTask();
        }
    }

    // ===== UI 管理器 =====
    class UIManager {
        constructor(stateManager, automationController) {
            this.state = stateManager;
            this.controller = automationController;
            this.panel = null;
            this.statusElement = null;
            this.continuousButton = null;
            this.singleButton = null;

            this.state.ui = this;
        }

        create() {
            if (document.getElementById('automation-control-panel')) {
                return;
            }

            this.createPanel();
            this.createDragHandle();
            this.createModeSelector();
            this.createQuestionConfig();
            this.createButtons();
            this.createStatusIndicator();
            this.addStyles();
            this.enableDragging();

            document.body.appendChild(this.panel);
            this.updateState(this.state.isRunning, this.state.currentMode);

            Logger.debug('UI 创建完成');
        }

        createPanel() {
            this.panel = document.createElement('div');
            this.panel.id = 'automation-control-panel';
        }

        createDragHandle() {
            const handle = document.createElement('div');
            handle.id = 'drag-handle';
            handle.innerHTML = `
                <span class="drag-icon">🎯</span>
                <span class="title-text">腾讯质检助手 v19.0</span>
                <span class="drag-hint">⋮⋮</span>
            `;
            handle.title = '拖拽移动面板';
            this.panel.appendChild(handle);
        }

        createModeSelector() {
            const container = document.createElement('div');
            container.className = 'config-section';
            container.innerHTML = `
                <div id="mode-selector">
                    <button id="mode-modify" class="mode-button ${this.state.currentMode === 'modify' ? 'active' : ''}" data-mode="modify">
                        <span class="mode-icon">✏️</span>
                        <span class="mode-text">修改标注</span>
                    </button>
                    <button id="mode-auto-pass" class="mode-button ${this.state.currentMode === 'auto-pass' ? 'active' : ''}" data-mode="auto-pass">
                        <span class="mode-icon">✅</span>
                        <span class="mode-text">自动通过</span>
                    </button>
                </div>
            `;

            this.panel.appendChild(container);
            this.bindModeEvents();
        }

        bindModeEvents() {
            const modifyBtn = this.panel.querySelector('#mode-modify');
            const autoPassBtn = this.panel.querySelector('#mode-auto-pass');

            modifyBtn.addEventListener('click', () => this.setMode('modify'));
            autoPassBtn.addEventListener('click', () => this.setMode('auto-pass'));
        }

        setMode(mode) {
            this.state.setMode(mode);

            const modifyBtn = this.panel.querySelector('#mode-modify');
            const autoPassBtn = this.panel.querySelector('#mode-auto-pass');
            const questionConfig = this.panel.querySelector('#question-config');

            if (mode === 'modify') {
                modifyBtn.classList.add('active');
                autoPassBtn.classList.remove('active');
                if (questionConfig) questionConfig.style.display = 'block';
                if (this.singleButton) this.singleButton.style.display = 'block';
            } else {
                modifyBtn.classList.remove('active');
                autoPassBtn.classList.add('active');
                if (questionConfig) questionConfig.style.display = 'none';
                if (this.singleButton) this.singleButton.style.display = 'none';
            }

            this.updateStatus(`切换到${mode === 'modify' ? '修改标注' : '自动通过'}模式`, 'success');
        }

        createQuestionConfig() {
            const container = document.createElement('div');
            container.id = 'question-config';
            container.className = 'config-section';

            const questionNumbers = [1, 8];
            const choices = ConfigManager.get('defaultChoices');

            for (let i = 0; i < 2; i++) {
                const questionNum = questionNumbers[i];
                const choice = choices[i];

                const questionCard = document.createElement('div');
                questionCard.className = 'question-card';
                questionCard.innerHTML = `
                    <div class="question-header">
                        <input type="checkbox" id="q${questionNum}_enable" class="enable-checkbox" checked>
                        <label for="q${questionNum}_enable" class="q-label">
                            <span class="q-number">Q${questionNum}</span>问题${questionNum}
                        </label>
                    </div>
                    <div class="radio-group">
                        <div class="radio-option ${choice === '1' ? 'selected' : ''}">
                            <input type="radio" id="q${questionNum}_opt1" name="q${questionNum}_options" value="1" ${choice === '1' ? 'checked' : ''}>
                            <label for="q${questionNum}_opt1"><span class="option-badge">选项1</span></label>
                        </div>
                        <div class="radio-option ${choice === '0' ? 'selected' : ''}">
                            <input type="radio" id="q${questionNum}_opt0" name="q${questionNum}_options" value="0" ${choice === '0' ? 'checked' : ''}>
                            <label for="q${questionNum}_opt0"><span class="option-badge">选项0</span></label>
                        </div>
                    </div>
                `;

                container.appendChild(questionCard);
            }

            this.panel.appendChild(container);
        }

        createButtons() {
            const container = document.createElement('div');
            container.className = 'button-container';

            this.continuousButton = document.createElement('button');
            this.continuousButton.id = 'continuous-button';
            this.continuousButton.innerHTML = '<span class="btn-icon">🚀</span><span class="btn-text">启动连续处理</span>';
            this.continuousButton.addEventListener('click', () => this.toggleAutomation());

            this.singleButton = document.createElement('button');
            this.singleButton.id = 'single-button';
            this.singleButton.innerHTML = '<span class="btn-icon">⚡️</span><span class="btn-text">单次处理</span>';
            this.singleButton.addEventListener('click', () => this.controller.processSingle());

            container.appendChild(this.continuousButton);
            container.appendChild(this.singleButton);
            this.panel.appendChild(container);
        }

        createStatusIndicator() {
            this.statusElement = document.createElement('div');
            this.statusElement.id = 'status-indicator';
            this.statusElement.innerHTML = '<span class="status-icon">🎯</span><span class="status-text">待命中</span>';
            this.panel.appendChild(this.statusElement);
        }

        toggleAutomation() {
            if (this.state.isRunning) {
                this.controller.stop();
            } else {
                this.controller.start();
            }
        }

        updateState(isRunning, currentMode) {
            if (!this.continuousButton || !this.singleButton) return;

            if (isRunning) {
                this.continuousButton.innerHTML = '<span class="btn-icon">🛑</span><span class="btn-text">停止处理</span>';
                this.continuousButton.className = 'button-stop';
                this.singleButton.disabled = true;
            } else {
                this.continuousButton.innerHTML = '<span class="btn-icon">🚀</span><span class="btn-text">启动连续处理</span>';
                this.continuousButton.className = 'button-start';
                this.singleButton.disabled = false;
            }

            // 更新模式显示
            const questionConfig = this.panel?.querySelector('#question-config');
            if (questionConfig) {
                questionConfig.style.display = currentMode === 'modify' ? 'block' : 'none';
            }

            if (this.singleButton) {
                this.singleButton.style.display = currentMode === 'modify' ? 'block' : 'none';
            }
        }

        updateStatus(message, type = 'info') {
            if (!this.statusElement) return;

            const icons = {
                info: '🎯',
                success: '✅',
                warning: '⚠️',
                error: '❌'
            };

            const colors = {
                info: '#6b7280',
                success: '#10b981',
                warning: '#f59e0b',
                error: '#ef4444'
            };

            this.statusElement.innerHTML = `
                <span class="status-icon">${icons[type]}</span>
                <span class="status-text">${message}</span>
            `;
            this.statusElement.style.color = colors[type];

            Logger.log(`状态更新: ${message}`);
        }

        enableDragging() {
            const handle = this.panel.querySelector('#drag-handle');
            let isDragging = false;
            let startX, startY, startLeft, startTop;

            handle.addEventListener('mousedown', (e) => {
                if (e.target.tagName === 'BUTTON') return;

                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;

                const rect = this.panel.getBoundingClientRect();
                startLeft = rect.left;
                startTop = rect.top;

                this.panel.style.cursor = 'grabbing';
                e.preventDefault();
            });

            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;

                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;

                const newLeft = Math.max(0, Math.min(startLeft + deltaX, window.innerWidth - this.panel.offsetWidth));
                const newTop = Math.max(0, Math.min(startTop + deltaY, window.innerHeight - this.panel.offsetHeight));

                this.panel.style.left = newLeft + 'px';
                this.panel.style.top = newTop + 'px';
                this.panel.style.right = 'auto';
            });

            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    this.panel.style.cursor = 'grab';
                }
            });
        }

        addStyles() {
            GM_addStyle(`
                #automation-control-panel {
                    position: fixed; top: 80px; right: 15px; z-index: 9999;
                    background: linear-gradient(145deg, #ffffff, #f8f9fa);
                    border: 1px solid #e3e6ea; border-radius: 12px; padding: 0;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.12), 0 4px 10px rgba(0,0,0,0.08);
                    width: 280px; cursor: grab; user-select: none;
                    backdrop-filter: blur(10px);
                }

                #automation-control-panel:active { cursor: grabbing; }

                #drag-handle {
                    background: linear-gradient(135deg, #4f46e5, #7c3aed);
                    color: white; padding: 12px 16px; font-size: 13px; font-weight: 600;
                    border-top-left-radius: 12px; border-top-right-radius: 12px;
                    cursor: grab; display: flex; align-items: center; justify-content: space-between;
                    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
                }

                #drag-handle:active { cursor: grabbing; }

                .drag-icon, .drag-hint { font-size: 14px; opacity: 0.9; }
                .title-text { flex: 1; text-align: center; font-weight: 700; }

                .config-section { padding: 20px; }

                #mode-selector {
                    display: flex; justify-content: center; margin-bottom: 16px;
                    background-color: #e9ecef; border-radius: 8px; padding: 4px;
                }

                .mode-button {
                    flex: 1; padding: 8px 12px; border: none; background-color: transparent;
                    color: #495057; font-size: 13px; font-weight: 600; cursor: pointer;
                    border-radius: 6px; transition: all 0.2s ease-in-out;
                    display: flex; align-items: center; justify-content: center; gap: 6px;
                }

                .mode-button:hover { background-color: #dee2e6; }
                .mode-button.active {
                    background-color: #ffffff; color: #4f46e5;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }

                #question-config { display: flex; flex-direction: column; gap: 16px; }

                .question-card {
                    background: linear-gradient(145deg, #f9fafb, #ffffff);
                    border: 1px solid #e5e7eb; border-radius: 8px; padding: 12px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05); transition: all 0.2s ease;
                }

                .question-card:hover { box-shadow: 0 4px 8px rgba(0,0,0,0.1); }

                .question-header {
                    display: flex; align-items: center; gap: 8px; margin-bottom: 10px;
                }

                .enable-checkbox { cursor: pointer; transform: scale(1.2); accent-color: #4f46e5; }

                .q-label {
                    font-size: 14px; cursor: pointer; font-weight: 600; color: #374151;
                    display: flex; align-items: center; gap: 6px;
                }

                .q-number {
                    background: linear-gradient(135deg, #4f46e5, #7c3aed);
                    color: white; font-size: 11px; padding: 2px 6px;
                    border-radius: 4px; font-weight: 700;
                }

                .radio-group { display: flex; gap: 8px; justify-content: center; }

                .radio-option {
                    display: flex; align-items: center; gap: 4px; padding: 6px 12px;
                    border-radius: 6px; cursor: pointer; transition: all 0.2s ease;
                    border: 1px solid #e5e7eb; background: #ffffff;
                }

                .radio-option:hover { background: #f3f4f6; border-color: #d1d5db; }
                .radio-option.selected {
                    background: linear-gradient(135deg, #ddd6fe, #e0e7ff);
                    border-color: #7c3aed; box-shadow: 0 2px 4px rgba(124, 58, 237, 0.2);
                }

                .radio-option input { cursor: pointer; accent-color: #4f46e5; }
                .radio-option label {
                    cursor: pointer; font-size: 13px; font-weight: 500;
                    color: #374151; margin: 0;
                }

                .option-badge {
                    padding: 2px 8px; border-radius: 4px; font-size: 11px;
                    background: #f3f4f6; color: #6b7280; font-weight: 600;
                }

                .radio-option.selected .option-badge { background: #4f46e5; color: white; }

                .button-container {
                    padding: 0 20px 16px 20px; display: flex; flex-direction: column; gap: 10px;
                }

                #continuous-button, #single-button {
                    color: white; border: none; border-radius: 8px; padding: 12px 16px;
                    font-size: 14px; font-weight: 600; cursor: pointer; width: 100%;
                    transition: all 0.3s ease; display: flex; align-items: center;
                    justify-content: center; gap: 8px;
                }

                .button-start {
                    background: linear-gradient(135deg, #10b981, #059669);
                    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                }

                .button-start:hover {
                    transform: translateY(-1px); box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
                }

                .button-stop {
                    background: linear-gradient(135deg, #ef4444, #dc2626);
                    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
                }

                .button-stop:hover {
                    transform: translateY(-1px); box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
                }

                #single-button {
                    background: linear-gradient(135deg, #3b82f6, #2563eb);
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
                }

                #single-button:hover {
                    transform: translateY(-1px); box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
                }

                #continuous-button:disabled, #single-button:disabled {
                    opacity: 0.6; cursor: not-allowed; transform: none;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }

                .btn-icon { font-size: 16px; }
                .btn-text { font-weight: 600; }

                #status-indicator {
                    padding: 10px 20px 20px 20px; text-align: center; font-size: 13px;
                    font-weight: 600; display: flex; align-items: center; justify-content: center;
                    gap: 6px; background: #f9fafb; margin: 0 12px 12px 12px;
                    border-radius: 6px; border: 1px solid #e5e7eb;
                }

                .status-icon { font-size: 14px; }
                .status-text { font-weight: 600; }
            `);
        }
    }

    // ===== 应用程序主类 =====
    class TencentQualityApp {
        constructor() {
            this.stateManager = new StateManager();
            this.processingEngine = new ProcessingEngine(this.stateManager);
            this.automationController = new AutomationController(this.stateManager, this.processingEngine);
            this.uiManager = new UIManager(this.stateManager, this.automationController);

            this.initCheckTimer = null;
        }

        init() {
            Logger.log('腾讯质检助手 v19.0 启动');
            Logger.debug(`当前URL: ${window.location.href}`);
            Logger.debug(`页面标题: ${document.title}`);

            // 等待页面完全加载后再创建UI
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.startUICheck());
            } else {
                this.startUICheck();
            }
        }

        startUICheck() {
            // 定期检查并创建UI，确保在页面动态加载时也能正常工作
            this.initCheckTimer = setInterval(() => {
                try {
                    this.uiManager.create();
                } catch (error) {
                    Logger.error('创建UI时出错', error);
                }
            }, 2000);

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', () => this.cleanup());
        }

        cleanup() {
            if (this.initCheckTimer) {
                clearInterval(this.initCheckTimer);
                this.initCheckTimer = null;
            }

            this.automationController.stop();
            this.stateManager.clearAllTimers();

            Logger.log('应用程序已清理');
        }
    }

    // ===== 程序入口 =====
    try {
        const app = new TencentQualityApp();
        app.init();
    } catch (error) {
        console.error('[FATAL ERROR v19.0] 应用程序启动失败:', error);
    }

})();
